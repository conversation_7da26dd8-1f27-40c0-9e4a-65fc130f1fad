多星座模式训练实验
================================================================================
实验时间: 2025_08_12_20_34_08
设备: cuda
问题规模: 150节点, 3卫星
训练配置: 3轮, 批次大小64
使用Transformer: True
Transformer配置: 2层, 4头

================================================================================
开始训练星座模式: COOPERATIVE
================================================================================
cooperative 模式模型信息:
  Actor参数数量: 3,730,953
  Critic参数数量: 494,285
  总参数数量: 4,225,238
详细训练配置:
  数据集大小: 训练100000, 验证10000
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 cooperative 模式...
训练开始时间: 2025-08-12 20:43:04
详细训练过程:
[COOPERATIVE] 开始训练 Epoch 1/3
[COOPERATIVE] Epoch 1, Batch 10/1563, loss: 1208.976, reward: 12.132, critic_reward: 8.539, revenue_rate: 0.2081, distance: 4.5467, memory: -0.0545, power: 0.1391, lr: 0.000100, took: 112.487s
[COOPERATIVE] Epoch 1, Batch 20/1563, loss: 28.565, reward: 18.369, critic_reward: 17.611, revenue_rate: 0.3100, distance: 6.3972, memory: -0.1347, power: 0.1932, lr: 0.000100, took: 171.637s
[COOPERATIVE] Epoch 1, Batch 30/1563, loss: 41.580, reward: 24.046, critic_reward: 22.048, revenue_rate: 0.4102, distance: 8.7131, memory: -0.0605, power: 0.2615, lr: 0.000100, took: 235.635s
[COOPERATIVE] Epoch 1, Batch 40/1563, loss: 24.429, reward: 26.299, critic_reward: 26.198, revenue_rate: 0.4464, distance: 9.2539, memory: -0.0417, power: 0.2787, lr: 0.000100, took: 304.448s
[COOPERATIVE] Epoch 1, Batch 50/1563, loss: 54.133, reward: 29.078, critic_reward: 30.494, revenue_rate: 0.4890, distance: 9.7707, memory: -0.0281, power: 0.2959, lr: 0.000100, took: 253.055s
[COOPERATIVE] Epoch 1, Batch 60/1563, loss: 38.860, reward: 34.535, critic_reward: 34.155, revenue_rate: 0.5849, distance: 11.8580, memory: 0.0294, power: 0.3588, lr: 0.000100, took: 359.507s
[COOPERATIVE] Epoch 1, Batch 70/1563, loss: 20.795, reward: 34.530, critic_reward: 33.919, revenue_rate: 0.5812, distance: 11.5440, memory: 0.0292, power: 0.3457, lr: 0.000100, took: 349.013s
[COOPERATIVE] Epoch 1, Batch 80/1563, loss: 68.096, reward: 34.957, critic_reward: 38.492, revenue_rate: 0.5855, distance: 11.1099, memory: 0.0035, power: 0.3353, lr: 0.000100, took: 325.539s
[COOPERATIVE] Epoch 1, Batch 90/1563, loss: 11.436, reward: 34.463, critic_reward: 33.257, revenue_rate: 0.5739, distance: 10.5396, memory: -0.0014, power: 0.3208, lr: 0.000100, took: 294.814s
[COOPERATIVE] Epoch 1, Batch 100/1563, loss: 11.781, reward: 37.760, critic_reward: 38.442, revenue_rate: 0.6301, distance: 11.8702, memory: 0.0298, power: 0.3588, lr: 0.000100, took: 365.891s
[COOPERATIVE] Epoch 1, Batch 110/1563, loss: 15.372, reward: 38.233, critic_reward: 38.617, revenue_rate: 0.6378, distance: 11.9901, memory: 0.0322, power: 0.3614, lr: 0.000100, took: 373.423s
❌ cooperative 模式训练失败: CUDA out of memory. Tried to allocate 30.00 MiB. GPU 0 has a total capacity of 6.00 GiB of which 0 bytes is free. Of the allocated memory 19.68 GiB is allocated by PyTorch, and 893.88 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0811single_smp\train_multi_constellation_modes.py", line 832, in main
    result = train_single_mode(mode, base_save_dir, transformer_config)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0811single_smp\train_multi_constellation_modes.py", line 348, in train_single_mode
    best_reward, training_stats = train_constellation_with_detailed_logging(
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0811single_smp\train_multi_constellation_modes.py", line 141, in train_constellation_with_detailed_logging
    critic_est = critic(static, dynamic).view(-1)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0811single_smp\constellation_smp\gpn_constellation.py", line 421, in forward
    constellation_features, _ = self.constellation_encoder(static, dynamic)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0811single_smp\constellation_smp\gpn_constellation.py", line 110, in forward
    sat_features_attn = self.apply_attention(satellite_features_stack, batch_size, seq_len)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0811single_smp\constellation_smp\gpn_constellation.py", line 186, in apply_attention
    features_attn, _ = self.inter_satellite_attention(
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\activation.py", line 1368, in forward
    attn_output, attn_output_weights = F.multi_head_attention_forward(
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\functional.py", line 6249, in multi_head_attention_forward
    attn_output.transpose(0, 1).contiguous().view(tgt_len * bsz, embed_dim)
torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 30.00 MiB. GPU 0 has a total capacity of 6.00 GiB of which 0 bytes is free. Of the allocated memory 19.68 GiB is allocated by PyTorch, and 893.88 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)


================================================================================
开始训练星座模式: COMPETITIVE
================================================================================
competitive 模式模型信息:
  Actor参数数量: 3,730,953
  Critic参数数量: 494,285
  总参数数量: 4,225,238
详细训练配置:
  数据集大小: 训练100000, 验证10000
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 competitive 模式...
训练开始时间: 2025-08-12 21:50:53
详细训练过程:
[COMPETITIVE] 开始训练 Epoch 1/3
[COMPETITIVE] Epoch 1, Batch 10/1563, loss: 1832.337, reward: 10.698, critic_reward: -6.878, revenue_rate: 0.1861, distance: 4.2865, memory: 0.0169, power: 0.1301, lr: 0.000100, took: 148.493s
[COMPETITIVE] Epoch 1, Batch 20/1563, loss: 73.988, reward: 15.786, critic_reward: 17.850, revenue_rate: 0.2673, distance: 5.6386, memory: -0.1310, power: 0.1701, lr: 0.000100, took: 184.574s
[COMPETITIVE] Epoch 1, Batch 30/1563, loss: 20.259, reward: 19.065, critic_reward: 17.325, revenue_rate: 0.3228, distance: 6.6364, memory: -0.1148, power: 0.2008, lr: 0.000100, took: 206.977s
[COMPETITIVE] Epoch 1, Batch 40/1563, loss: 41.209, reward: 18.856, critic_reward: 22.058, revenue_rate: 0.3174, distance: 6.3718, memory: -0.1265, power: 0.1912, lr: 0.000100, took: 206.019s
[COMPETITIVE] Epoch 1, Batch 50/1563, loss: 41.550, reward: 22.906, critic_reward: 21.239, revenue_rate: 0.3830, distance: 7.4629, memory: -0.0981, power: 0.2251, lr: 0.000100, took: 224.692s
[COMPETITIVE] Epoch 1, Batch 60/1563, loss: 20.836, reward: 29.959, critic_reward: 28.984, revenue_rate: 0.5012, distance: 9.5107, memory: -0.0345, power: 0.2870, lr: 0.000100, took: 268.489s
[COMPETITIVE] Epoch 1, Batch 70/1563, loss: 17.349, reward: 32.512, critic_reward: 31.521, revenue_rate: 0.5422, distance: 10.2313, memory: -0.0193, power: 0.3075, lr: 0.000100, took: 278.611s
[COMPETITIVE] Epoch 1, Batch 80/1563, loss: 19.100, reward: 33.553, critic_reward: 35.718, revenue_rate: 0.5593, distance: 10.3017, memory: -0.0128, power: 0.3112, lr: 0.000100, took: 279.158s
[COMPETITIVE] Epoch 1, Batch 90/1563, loss: 28.267, reward: 31.158, critic_reward: 32.140, revenue_rate: 0.5191, distance: 9.5870, memory: -0.0353, power: 0.2929, lr: 0.000100, took: 287.966s
[COMPETITIVE] Epoch 1, Batch 100/1563, loss: 28.710, reward: 30.811, critic_reward: 29.085, revenue_rate: 0.5133, distance: 9.6281, memory: -0.0411, power: 0.2909, lr: 0.000100, took: 283.352s
[COMPETITIVE] Epoch 1, Batch 110/1563, loss: 21.763, reward: 33.679, critic_reward: 32.767, revenue_rate: 0.5614, distance: 10.5396, memory: -0.0053, power: 0.3177, lr: 0.000100, took: 287.072s
[COMPETITIVE] Epoch 1, Batch 120/1563, loss: 17.363, reward: 38.642, critic_reward: 38.149, revenue_rate: 0.6467, distance: 12.3046, memory: 0.0450, power: 0.3741, lr: 0.000100, took: 355.112s
[COMPETITIVE] Epoch 1, Batch 130/1563, loss: 14.030, reward: 39.554, critic_reward: 39.703, revenue_rate: 0.6603, distance: 12.4814, memory: 0.0445, power: 0.3788, lr: 0.000100, took: 358.635s
[COMPETITIVE] Epoch 1, Batch 140/1563, loss: 17.612, reward: 40.015, critic_reward: 39.368, revenue_rate: 0.6677, distance: 12.5551, memory: 0.0465, power: 0.3799, lr: 0.000100, took: 362.702s
[COMPETITIVE] Epoch 1, Batch 150/1563, loss: 7.722, reward: 37.457, critic_reward: 36.819, revenue_rate: 0.6239, distance: 11.6209, memory: 0.0298, power: 0.3499, lr: 0.000100, took: 310.215s
[COMPETITIVE] Epoch 1, Batch 160/1563, loss: 8.803, reward: 36.722, critic_reward: 37.072, revenue_rate: 0.6126, distance: 11.4133, memory: 0.0167, power: 0.3444, lr: 0.000100, took: 308.412s
[COMPETITIVE] Epoch 1, Batch 170/1563, loss: 6.946, reward: 38.526, critic_reward: 38.518, revenue_rate: 0.6452, distance: 12.0704, memory: 0.0422, power: 0.3654, lr: 0.000100, took: 343.020s
[COMPETITIVE] Epoch 1, Batch 180/1563, loss: 7.125, reward: 37.012, critic_reward: 36.560, revenue_rate: 0.6190, distance: 11.4936, memory: 0.0208, power: 0.3443, lr: 0.000100, took: 313.135s
[COMPETITIVE] Epoch 1, Batch 190/1563, loss: 15.885, reward: 33.752, critic_reward: 33.872, revenue_rate: 0.5609, distance: 10.1932, memory: -0.0126, power: 0.3089, lr: 0.000100, took: 278.582s
[COMPETITIVE] Epoch 1, Batch 200/1563, loss: 13.546, reward: 37.800, critic_reward: 36.619, revenue_rate: 0.6280, distance: 11.5385, memory: 0.0283, power: 0.3482, lr: 0.000100, took: 309.721s
[COMPETITIVE] Epoch 1, Batch 210/1563, loss: 13.586, reward: 39.006, critic_reward: 38.917, revenue_rate: 0.6504, distance: 11.9710, memory: 0.0436, power: 0.3628, lr: 0.000100, took: 339.532s
[COMPETITIVE] Epoch 1, Batch 220/1563, loss: 23.831, reward: 42.264, critic_reward: 44.266, revenue_rate: 0.7084, distance: 13.2557, memory: 0.0749, power: 0.4004, lr: 0.000100, took: 376.705s
[COMPETITIVE] Epoch 1, Batch 230/1563, loss: 8.987, reward: 41.628, critic_reward: 42.524, revenue_rate: 0.6948, distance: 12.8914, memory: 0.0700, power: 0.3915, lr: 0.000100, took: 374.636s
[COMPETITIVE] Epoch 1, Batch 240/1563, loss: 5.666, reward: 40.503, critic_reward: 40.495, revenue_rate: 0.6742, distance: 12.3315, memory: 0.0531, power: 0.3757, lr: 0.000100, took: 360.034s
[COMPETITIVE] Epoch 1, Batch 250/1563, loss: 15.252, reward: 41.379, critic_reward: 40.251, revenue_rate: 0.6901, distance: 12.8687, memory: 0.0679, power: 0.3882, lr: 0.000100, took: 374.003s
[COMPETITIVE] Epoch 1, Batch 260/1563, loss: 12.441, reward: 40.913, critic_reward: 41.639, revenue_rate: 0.6823, distance: 12.5299, memory: 0.0531, power: 0.3792, lr: 0.000100, took: 362.403s
[COMPETITIVE] Epoch 1, Batch 270/1563, loss: 8.715, reward: 39.271, critic_reward: 40.387, revenue_rate: 0.6548, distance: 12.0495, memory: 0.0330, power: 0.3613, lr: 0.000100, took: 333.295s
[COMPETITIVE] Epoch 1, Batch 280/1563, loss: 6.662, reward: 40.019, critic_reward: 40.343, revenue_rate: 0.6660, distance: 12.3151, memory: 0.0442, power: 0.3711, lr: 0.000100, took: 350.793s
[COMPETITIVE] Epoch 1, Batch 290/1563, loss: 24.109, reward: 37.834, critic_reward: 36.018, revenue_rate: 0.6307, distance: 11.6786, memory: 0.0256, power: 0.3539, lr: 0.000100, took: 323.933s
[COMPETITIVE] Epoch 1, Batch 300/1563, loss: 11.507, reward: 41.509, critic_reward: 42.423, revenue_rate: 0.6918, distance: 12.9956, memory: 0.0651, power: 0.3950, lr: 0.000100, took: 377.267s
[COMPETITIVE] Epoch 1, Batch 310/1563, loss: 14.297, reward: 39.627, critic_reward: 41.217, revenue_rate: 0.6599, distance: 12.0172, memory: 0.0412, power: 0.3678, lr: 0.000100, took: 344.029s
[COMPETITIVE] Epoch 1, Batch 320/1563, loss: 8.411, reward: 39.070, critic_reward: 37.639, revenue_rate: 0.6494, distance: 11.9682, memory: 0.0363, power: 0.3598, lr: 0.000100, took: 334.508s
[COMPETITIVE] Epoch 1, Batch 330/1563, loss: 7.666, reward: 41.247, critic_reward: 40.505, revenue_rate: 0.6878, distance: 12.6557, memory: 0.0573, power: 0.3821, lr: 0.000100, took: 365.933s
[COMPETITIVE] Epoch 1, Batch 340/1563, loss: 10.378, reward: 41.492, critic_reward: 41.853, revenue_rate: 0.6939, distance: 12.8030, memory: 0.0637, power: 0.3865, lr: 0.000100, took: 365.629s
[COMPETITIVE] Epoch 1, Batch 350/1563, loss: 8.952, reward: 43.210, critic_reward: 42.778, revenue_rate: 0.7192, distance: 13.2912, memory: 0.0725, power: 0.4047, lr: 0.000100, took: 385.562s
[COMPETITIVE] Epoch 1, Batch 360/1563, loss: 8.153, reward: 43.236, critic_reward: 43.179, revenue_rate: 0.7192, distance: 13.2201, memory: 0.0644, power: 0.4024, lr: 0.000100, took: 384.831s
[COMPETITIVE] Epoch 1, Batch 370/1563, loss: 4.858, reward: 41.337, critic_reward: 41.811, revenue_rate: 0.6899, distance: 12.6180, memory: 0.0585, power: 0.3795, lr: 0.000100, took: 362.137s
[COMPETITIVE] Epoch 1, Batch 380/1563, loss: 6.776, reward: 40.480, critic_reward: 40.989, revenue_rate: 0.6719, distance: 12.2071, memory: 0.0330, power: 0.3667, lr: 0.000100, took: 349.211s
[COMPETITIVE] Epoch 1, Batch 390/1563, loss: 5.274, reward: 38.517, critic_reward: 38.243, revenue_rate: 0.6386, distance: 11.5261, memory: 0.0232, power: 0.3481, lr: 0.000100, took: 309.075s
[COMPETITIVE] Epoch 1, Batch 400/1563, loss: 6.618, reward: 38.235, critic_reward: 37.446, revenue_rate: 0.6350, distance: 11.5060, memory: 0.0309, power: 0.3485, lr: 0.000100, took: 315.335s
[COMPETITIVE] Epoch 1, Batch 410/1563, loss: 5.401, reward: 40.540, critic_reward: 40.684, revenue_rate: 0.6768, distance: 12.4563, memory: 0.0535, power: 0.3781, lr: 0.000100, took: 359.743s
[COMPETITIVE] Epoch 1, Batch 420/1563, loss: 9.279, reward: 40.940, critic_reward: 40.491, revenue_rate: 0.6838, distance: 12.6826, memory: 0.0473, power: 0.3825, lr: 0.000100, took: 365.051s
[COMPETITIVE] Epoch 1, Batch 430/1563, loss: 15.535, reward: 41.032, critic_reward: 41.624, revenue_rate: 0.6832, distance: 12.6205, memory: 0.0631, power: 0.3802, lr: 0.000100, took: 362.679s
[COMPETITIVE] Epoch 1, Batch 440/1563, loss: 10.270, reward: 41.853, critic_reward: 40.790, revenue_rate: 0.7000, distance: 13.0468, memory: 0.0728, power: 0.3966, lr: 0.000100, took: 378.966s
[COMPETITIVE] Epoch 1, Batch 450/1563, loss: 13.897, reward: 42.133, critic_reward: 43.494, revenue_rate: 0.7008, distance: 12.8439, memory: 0.0623, power: 0.3902, lr: 0.000100, took: 372.617s
[COMPETITIVE] Epoch 1, Batch 460/1563, loss: 13.794, reward: 41.935, critic_reward: 40.027, revenue_rate: 0.6977, distance: 12.8026, memory: 0.0665, power: 0.3885, lr: 0.000100, took: 369.190s
[COMPETITIVE] Epoch 1, Batch 470/1563, loss: 9.775, reward: 41.038, critic_reward: 39.942, revenue_rate: 0.6818, distance: 12.2771, memory: 0.0557, power: 0.3721, lr: 0.000100, took: 355.692s
[COMPETITIVE] Epoch 1, Batch 480/1563, loss: 9.179, reward: 40.658, critic_reward: 39.766, revenue_rate: 0.6732, distance: 12.1701, memory: 0.0514, power: 0.3676, lr: 0.000100, took: 349.434s
[COMPETITIVE] Epoch 1, Batch 490/1563, loss: 10.726, reward: 42.168, critic_reward: 42.368, revenue_rate: 0.7028, distance: 13.1205, memory: 0.0844, power: 0.3947, lr: 0.000100, took: 379.990s
❌ competitive 模式训练失败: CUDA out of memory. Tried to allocate 20.00 MiB. GPU 0 has a total capacity of 6.00 GiB of which 0 bytes is free. Of the allocated memory 19.72 GiB is allocated by PyTorch, and 857.48 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0811single_smp\train_multi_constellation_modes.py", line 832, in main
    result = train_single_mode(mode, base_save_dir, transformer_config)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0811single_smp\train_multi_constellation_modes.py", line 348, in train_single_mode
    best_reward, training_stats = train_constellation_with_detailed_logging(
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0811single_smp\train_multi_constellation_modes.py", line 132, in train_constellation_with_detailed_logging
    tour_indices, satellite_indices, tour_log_prob, satellite_log_prob = actor(static, dynamic)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0811single_smp\constellation_smp\gpn_constellation.py", line 300, in forward
    task_logits, task_hx = self.task_selector(
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0811single_smp\gpn.py", line 309, in forward
    probs, _ = self.pointer(rnn_out, context)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0811single_smp\gpn.py", line 515, in forward
    context_proj = self.w_context(context.transpose(1, 2)).view(batch_size, seq_len, n_head, d_hidden_size)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 20.00 MiB. GPU 0 has a total capacity of 6.00 GiB of which 0 bytes is free. Of the allocated memory 19.72 GiB is allocated by PyTorch, and 857.48 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)


================================================================================
开始训练星座模式: HYBRID
================================================================================
hybrid 模式模型信息:
  Actor参数数量: 3,927,817
  Critic参数数量: 691,149
  总参数数量: 4,618,966
详细训练配置:
  数据集大小: 训练100000, 验证10000
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 hybrid 模式...
训练开始时间: 2025-08-13 02:26:41
详细训练过程:
[HYBRID] 开始训练 Epoch 1/3
[HYBRID] Epoch 1, Batch 10/1563, loss: 1652.299, reward: 12.382, critic_reward: 25.241, revenue_rate: 0.2140, distance: 4.9184, memory: 0.0042, power: 0.1478, lr: 0.000100, took: 174.273s
[HYBRID] Epoch 1, Batch 20/1563, loss: 297.004, reward: 18.547, critic_reward: 21.616, revenue_rate: 0.3149, distance: 6.5693, memory: -0.1114, power: 0.1987, lr: 0.000100, took: 211.508s
[HYBRID] Epoch 1, Batch 30/1563, loss: 141.586, reward: 24.607, critic_reward: 21.456, revenue_rate: 0.4143, distance: 8.3088, memory: -0.0721, power: 0.2508, lr: 0.000100, took: 264.108s
[HYBRID] Epoch 1, Batch 40/1563, loss: 175.220, reward: 28.955, critic_reward: 30.547, revenue_rate: 0.4872, distance: 9.5771, memory: -0.0263, power: 0.2904, lr: 0.000100, took: 302.155s
[HYBRID] Epoch 1, Batch 50/1563, loss: 20.754, reward: 31.879, critic_reward: 32.623, revenue_rate: 0.5366, distance: 10.4480, memory: -0.0134, power: 0.3162, lr: 0.000100, took: 303.410s
[HYBRID] Epoch 1, Batch 60/1563, loss: 86.844, reward: 35.247, critic_reward: 39.894, revenue_rate: 0.5915, distance: 11.4204, memory: 0.0194, power: 0.3445, lr: 0.000100, took: 353.583s
[HYBRID] Epoch 1, Batch 70/1563, loss: 37.107, reward: 34.161, critic_reward: 32.527, revenue_rate: 0.5720, distance: 10.8208, memory: -0.0069, power: 0.3259, lr: 0.000100, took: 319.354s
[HYBRID] Epoch 1, Batch 80/1563, loss: 30.445, reward: 32.988, critic_reward: 30.999, revenue_rate: 0.5499, distance: 10.2667, memory: -0.0073, power: 0.3085, lr: 0.000100, took: 308.418s
[HYBRID] Epoch 1, Batch 90/1563, loss: 8.261, reward: 36.904, critic_reward: 36.835, revenue_rate: 0.6188, distance: 11.7304, memory: 0.0359, power: 0.3536, lr: 0.000100, took: 352.391s
[HYBRID] Epoch 1, Batch 100/1563, loss: 20.835, reward: 37.463, critic_reward: 38.298, revenue_rate: 0.6276, distance: 11.8314, memory: 0.0356, power: 0.3573, lr: 0.000100, took: 362.968s
[HYBRID] Epoch 1, Batch 110/1563, loss: 60.161, reward: 39.386, critic_reward: 40.449, revenue_rate: 0.6582, distance: 12.4586, memory: 0.0487, power: 0.3743, lr: 0.000100, took: 389.802s
[HYBRID] Epoch 1, Batch 120/1563, loss: 13.884, reward: 39.509, critic_reward: 38.587, revenue_rate: 0.6616, distance: 12.4824, memory: 0.0416, power: 0.3746, lr: 0.000100, took: 385.763s
[HYBRID] Epoch 1, Batch 130/1563, loss: 9.816, reward: 38.392, critic_reward: 38.435, revenue_rate: 0.6385, distance: 11.8114, memory: 0.0268, power: 0.3561, lr: 0.000100, took: 366.583s
[HYBRID] Epoch 1, Batch 140/1563, loss: 23.372, reward: 37.581, critic_reward: 38.093, revenue_rate: 0.6248, distance: 11.5171, memory: 0.0186, power: 0.3460, lr: 0.000100, took: 353.272s
[HYBRID] Epoch 1, Batch 150/1563, loss: 22.081, reward: 38.796, critic_reward: 39.005, revenue_rate: 0.6449, distance: 11.7927, memory: 0.0207, power: 0.3570, lr: 0.000100, took: 362.431s
[HYBRID] Epoch 1, Batch 160/1563, loss: 14.813, reward: 40.503, critic_reward: 41.076, revenue_rate: 0.6753, distance: 12.5214, memory: 0.0603, power: 0.3779, lr: 0.000100, took: 389.169s
[HYBRID] Epoch 1, Batch 170/1563, loss: 14.368, reward: 37.446, critic_reward: 36.304, revenue_rate: 0.6231, distance: 11.4341, memory: 0.0248, power: 0.3469, lr: 0.000100, took: 348.397s
[HYBRID] Epoch 1, Batch 180/1563, loss: 8.522, reward: 39.844, critic_reward: 40.014, revenue_rate: 0.6690, distance: 12.7274, memory: 0.0601, power: 0.3847, lr: 0.000100, took: 393.378s
[HYBRID] Epoch 1, Batch 190/1563, loss: 8.423, reward: 41.400, critic_reward: 40.698, revenue_rate: 0.6911, distance: 12.8448, memory: 0.0687, power: 0.3903, lr: 0.000100, took: 396.491s
❌ hybrid 模式训练失败: CUDA out of memory. Tried to allocate 132.00 MiB. GPU 0 has a total capacity of 6.00 GiB of which 0 bytes is free. Of the allocated memory 19.60 GiB is allocated by PyTorch, and 987.84 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0811single_smp\train_multi_constellation_modes.py", line 832, in main
    result = train_single_mode(mode, base_save_dir, transformer_config)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0811single_smp\train_multi_constellation_modes.py", line 348, in train_single_mode
    best_reward, training_stats = train_constellation_with_detailed_logging(
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0811single_smp\train_multi_constellation_modes.py", line 141, in train_constellation_with_detailed_logging
    critic_est = critic(static, dynamic).view(-1)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0811single_smp\constellation_smp\gpn_constellation.py", line 421, in forward
    constellation_features, _ = self.constellation_encoder(static, dynamic)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0811single_smp\constellation_smp\gpn_constellation.py", line 114, in forward
    sat_features_attn = self.apply_attention(satellite_features_stack, batch_size, seq_len)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0811single_smp\constellation_smp\gpn_constellation.py", line 186, in apply_attention
    features_attn, _ = self.inter_satellite_attention(
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\activation.py", line 1368, in forward
    attn_output, attn_output_weights = F.multi_head_attention_forward(
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\functional.py", line 6244, in multi_head_attention_forward
    attn_output_weights = dropout(attn_output_weights, p=dropout_p)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\functional.py", line 1425, in dropout
    _VF.dropout_(input, p, training) if inplace else _VF.dropout(input, p, training)
torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 132.00 MiB. GPU 0 has a total capacity of 6.00 GiB of which 0 bytes is free. Of the allocated memory 19.60 GiB is allocated by PyTorch, and 987.84 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)

❌ 所有模式训练都失败了
