多星座模式训练实验
================================================================================
实验时间: 2025_08_13_08_42_14
设备: cuda
问题规模: 100节点, 3卫星
训练配置: 3轮, 批次大小64
使用Transformer: True
Transformer配置: 2层, 4头

================================================================================
开始训练星座模式: COOPERATIVE
================================================================================
cooperative 模式模型信息:
  Actor参数数量: 3,730,953
  Critic参数数量: 494,285
  总参数数量: 4,225,238
详细训练配置:
  数据集大小: 训练100000, 验证10000
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 cooperative 模式...
训练开始时间: 2025-08-13 08:48:15
详细训练过程:
[COOPERATIVE] 开始训练 Epoch 1/3
[COOPERATIVE] Epoch 1, Batch 10/1563, loss: 564.392, reward: 11.939, critic_reward: 8.406, revenue_rate: 0.2993, distance: 4.2874, memory: -0.0951, power: 0.1301, lr: 0.000100, took: 63.659s
[COOPERATIVE] Epoch 1, Batch 20/1563, loss: 39.994, reward: 15.653, critic_reward: 13.838, revenue_rate: 0.3882, distance: 5.2723, memory: -0.1692, power: 0.1595, lr: 0.000100, took: 74.261s
[COOPERATIVE] Epoch 1, Batch 30/1563, loss: 81.671, reward: 17.907, critic_reward: 16.230, revenue_rate: 0.4433, distance: 5.9285, memory: -0.1447, power: 0.1802, lr: 0.000100, took: 88.741s
[COOPERATIVE] Epoch 1, Batch 40/1563, loss: 18.488, reward: 18.817, critic_reward: 16.990, revenue_rate: 0.4672, distance: 6.1093, memory: -0.1388, power: 0.1845, lr: 0.000100, took: 89.754s
[COOPERATIVE] Epoch 1, Batch 50/1563, loss: 12.131, reward: 23.290, critic_reward: 23.647, revenue_rate: 0.5762, distance: 7.4171, memory: -0.1072, power: 0.2246, lr: 0.000100, took: 104.249s
[COOPERATIVE] Epoch 1, Batch 60/1563, loss: 14.488, reward: 26.906, critic_reward: 27.381, revenue_rate: 0.6662, distance: 8.7034, memory: -0.0709, power: 0.2618, lr: 0.000100, took: 153.743s
[COOPERATIVE] Epoch 1, Batch 70/1563, loss: 27.435, reward: 25.224, critic_reward: 25.856, revenue_rate: 0.6266, distance: 8.3245, memory: -0.0761, power: 0.2512, lr: 0.000100, took: 162.205s
[COOPERATIVE] Epoch 1, Batch 80/1563, loss: 31.345, reward: 24.833, critic_reward: 23.207, revenue_rate: 0.6130, distance: 7.9663, memory: -0.0919, power: 0.2395, lr: 0.000100, took: 165.947s
[COOPERATIVE] Epoch 1, Batch 90/1563, loss: 23.700, reward: 27.046, critic_reward: 28.277, revenue_rate: 0.6666, distance: 8.4417, memory: -0.0836, power: 0.2554, lr: 0.000100, took: 158.072s
[COOPERATIVE] Epoch 1, Batch 100/1563, loss: 7.847, reward: 27.428, critic_reward: 28.471, revenue_rate: 0.6784, distance: 8.6961, memory: -0.0641, power: 0.2606, lr: 0.000100, took: 154.807s
[COOPERATIVE] Epoch 1, Batch 110/1563, loss: 10.125, reward: 28.223, critic_reward: 28.004, revenue_rate: 0.6977, distance: 8.9517, memory: -0.0600, power: 0.2681, lr: 0.000100, took: 153.112s
[COOPERATIVE] Epoch 1, Batch 120/1563, loss: 7.411, reward: 29.023, critic_reward: 28.524, revenue_rate: 0.7143, distance: 9.0028, memory: -0.0601, power: 0.2727, lr: 0.000100, took: 149.574s
[COOPERATIVE] Epoch 1, Batch 130/1563, loss: 10.536, reward: 28.808, critic_reward: 30.108, revenue_rate: 0.7130, distance: 9.1523, memory: -0.0416, power: 0.2775, lr: 0.000100, took: 152.417s
[COOPERATIVE] Epoch 1, Batch 140/1563, loss: 25.959, reward: 28.597, critic_reward: 28.139, revenue_rate: 0.7062, distance: 8.9796, memory: -0.0664, power: 0.2710, lr: 0.000100, took: 150.846s
[COOPERATIVE] Epoch 1, Batch 150/1563, loss: 33.928, reward: 27.444, critic_reward: 24.704, revenue_rate: 0.6796, distance: 8.5519, memory: -0.0749, power: 0.2596, lr: 0.000100, took: 154.619s
[COOPERATIVE] Epoch 1, Batch 160/1563, loss: 9.666, reward: 29.525, critic_reward: 29.791, revenue_rate: 0.7278, distance: 9.2073, memory: -0.0621, power: 0.2770, lr: 0.000100, took: 154.522s
[COOPERATIVE] Epoch 1, Batch 170/1563, loss: 29.588, reward: 27.542, critic_reward: 29.560, revenue_rate: 0.6764, distance: 8.2999, memory: -0.0830, power: 0.2508, lr: 0.000100, took: 166.811s
[COOPERATIVE] Epoch 1, Batch 180/1563, loss: 8.872, reward: 28.014, critic_reward: 28.166, revenue_rate: 0.6888, distance: 8.4952, memory: -0.0698, power: 0.2554, lr: 0.000100, took: 156.888s
[COOPERATIVE] Epoch 1, Batch 190/1563, loss: 14.121, reward: 30.279, critic_reward: 29.242, revenue_rate: 0.7476, distance: 9.6644, memory: -0.0245, power: 0.2906, lr: 0.000100, took: 161.589s
[COOPERATIVE] Epoch 1, Batch 200/1563, loss: 6.471, reward: 29.517, critic_reward: 29.534, revenue_rate: 0.7292, distance: 9.2949, memory: -0.0474, power: 0.2773, lr: 0.000100, took: 157.076s
[COOPERATIVE] Epoch 1, Batch 210/1563, loss: 7.574, reward: 28.809, critic_reward: 29.392, revenue_rate: 0.7087, distance: 8.8573, memory: -0.0664, power: 0.2684, lr: 0.000100, took: 164.956s
[COOPERATIVE] Epoch 1, Batch 220/1563, loss: 10.257, reward: 29.219, critic_reward: 27.412, revenue_rate: 0.7175, distance: 8.8610, memory: -0.0597, power: 0.2700, lr: 0.000100, took: 170.978s
[COOPERATIVE] Epoch 1, Batch 230/1563, loss: 12.058, reward: 30.988, critic_reward: 33.078, revenue_rate: 0.7687, distance: 9.8560, memory: -0.0278, power: 0.2979, lr: 0.000100, took: 160.138s
[COOPERATIVE] Epoch 1, Batch 240/1563, loss: 10.063, reward: 28.610, critic_reward: 29.684, revenue_rate: 0.7021, distance: 8.6932, memory: -0.0673, power: 0.2626, lr: 0.000100, took: 167.881s
[COOPERATIVE] Epoch 1, Batch 250/1563, loss: 5.661, reward: 28.294, critic_reward: 27.516, revenue_rate: 0.6953, distance: 8.5820, memory: -0.0766, power: 0.2573, lr: 0.000100, took: 167.135s
[COOPERATIVE] Epoch 1, Batch 260/1563, loss: 4.271, reward: 30.020, critic_reward: 29.789, revenue_rate: 0.7393, distance: 9.3612, memory: -0.0420, power: 0.2797, lr: 0.000100, took: 156.982s
[COOPERATIVE] Epoch 1, Batch 270/1563, loss: 6.215, reward: 30.904, critic_reward: 30.535, revenue_rate: 0.7605, distance: 9.6217, memory: -0.0351, power: 0.2885, lr: 0.000100, took: 160.973s
[COOPERATIVE] Epoch 1, Batch 280/1563, loss: 7.737, reward: 31.457, critic_reward: 31.520, revenue_rate: 0.7728, distance: 9.7131, memory: -0.0442, power: 0.2936, lr: 0.000100, took: 165.086s
[COOPERATIVE] Epoch 1, Batch 290/1563, loss: 4.791, reward: 28.929, critic_reward: 28.828, revenue_rate: 0.7097, distance: 8.6759, memory: -0.0650, power: 0.2646, lr: 0.000100, took: 184.160s
[COOPERATIVE] Epoch 1, Batch 300/1563, loss: 4.773, reward: 26.359, critic_reward: 26.664, revenue_rate: 0.6427, distance: 7.8657, memory: -0.0923, power: 0.2404, lr: 0.000100, took: 164.643s
[COOPERATIVE] Epoch 1, Batch 310/1563, loss: 10.115, reward: 23.916, critic_reward: 23.370, revenue_rate: 0.5845, distance: 7.0805, memory: -0.1040, power: 0.2158, lr: 0.000100, took: 133.011s
[COOPERATIVE] Epoch 1, Batch 320/1563, loss: 5.834, reward: 27.957, critic_reward: 28.408, revenue_rate: 0.6847, distance: 8.3329, memory: -0.0776, power: 0.2534, lr: 0.000100, took: 170.429s
[COOPERATIVE] Epoch 1, Batch 330/1563, loss: 8.422, reward: 30.616, critic_reward: 29.158, revenue_rate: 0.7576, distance: 9.4583, memory: -0.0401, power: 0.2865, lr: 0.000100, took: 163.655s
[COOPERATIVE] Epoch 1, Batch 340/1563, loss: 11.806, reward: 30.575, critic_reward: 31.549, revenue_rate: 0.7549, distance: 9.4785, memory: -0.0394, power: 0.2865, lr: 0.000100, took: 162.515s
[COOPERATIVE] Epoch 1, Batch 350/1563, loss: 10.855, reward: 31.282, critic_reward: 31.865, revenue_rate: 0.7759, distance: 10.1723, memory: -0.0084, power: 0.3067, lr: 0.000100, took: 171.602s
[COOPERATIVE] Epoch 1, Batch 360/1563, loss: 5.798, reward: 29.536, critic_reward: 29.291, revenue_rate: 0.7290, distance: 9.2660, memory: -0.0466, power: 0.2828, lr: 0.000100, took: 172.853s
[COOPERATIVE] Epoch 1, Batch 370/1563, loss: 6.353, reward: 25.385, critic_reward: 24.661, revenue_rate: 0.6226, distance: 7.5586, memory: -0.1031, power: 0.2305, lr: 0.000100, took: 144.614s
[COOPERATIVE] Epoch 1, Batch 380/1563, loss: 4.273, reward: 26.176, critic_reward: 25.369, revenue_rate: 0.6399, distance: 7.7450, memory: -0.0881, power: 0.2359, lr: 0.000100, took: 147.882s
[COOPERATIVE] Epoch 1, Batch 390/1563, loss: 5.061, reward: 29.985, critic_reward: 30.502, revenue_rate: 0.7365, distance: 9.0656, memory: -0.0500, power: 0.2764, lr: 0.000100, took: 172.107s
[COOPERATIVE] Epoch 1, Batch 400/1563, loss: 4.564, reward: 31.337, critic_reward: 31.524, revenue_rate: 0.7733, distance: 9.8895, memory: -0.0327, power: 0.2968, lr: 0.000100, took: 165.756s
[COOPERATIVE] Epoch 1, Batch 410/1563, loss: 11.413, reward: 30.693, critic_reward: 31.649, revenue_rate: 0.7528, distance: 9.3635, memory: -0.0410, power: 0.2857, lr: 0.000100, took: 168.442s
[COOPERATIVE] Epoch 1, Batch 420/1563, loss: 3.672, reward: 29.632, critic_reward: 29.475, revenue_rate: 0.7269, distance: 8.9706, memory: -0.0576, power: 0.2692, lr: 0.000100, took: 178.456s
[COOPERATIVE] Epoch 1, Batch 430/1563, loss: 5.381, reward: 28.340, critic_reward: 29.084, revenue_rate: 0.6951, distance: 8.6895, memory: -0.0602, power: 0.2630, lr: 0.000100, took: 177.979s
[COOPERATIVE] Epoch 1, Batch 440/1563, loss: 7.513, reward: 29.184, critic_reward: 30.403, revenue_rate: 0.7176, distance: 8.8397, memory: -0.0596, power: 0.2652, lr: 0.000100, took: 175.417s
[COOPERATIVE] Epoch 1, Batch 450/1563, loss: 5.890, reward: 28.704, critic_reward: 28.445, revenue_rate: 0.7054, distance: 8.6197, memory: -0.0709, power: 0.2609, lr: 0.000100, took: 176.823s
[COOPERATIVE] Epoch 1, Batch 460/1563, loss: 7.295, reward: 29.968, critic_reward: 29.565, revenue_rate: 0.7362, distance: 9.1496, memory: -0.0449, power: 0.2804, lr: 0.000100, took: 172.184s
[COOPERATIVE] Epoch 1, Batch 470/1563, loss: 5.908, reward: 29.849, critic_reward: 28.938, revenue_rate: 0.7316, distance: 9.0262, memory: -0.0538, power: 0.2745, lr: 0.000100, took: 181.095s
[COOPERATIVE] Epoch 1, Batch 480/1563, loss: 8.309, reward: 29.475, critic_reward: 30.659, revenue_rate: 0.7230, distance: 8.8486, memory: -0.0608, power: 0.2678, lr: 0.000100, took: 177.796s
[COOPERATIVE] Epoch 1, Batch 490/1563, loss: 5.615, reward: 27.193, critic_reward: 26.448, revenue_rate: 0.6645, distance: 8.0043, memory: -0.0830, power: 0.2408, lr: 0.000100, took: 165.441s
[COOPERATIVE] Epoch 1, Batch 500/1563, loss: 3.464, reward: 28.784, critic_reward: 28.821, revenue_rate: 0.7071, distance: 8.7471, memory: -0.0589, power: 0.2632, lr: 0.000100, took: 178.112s
[COOPERATIVE] Epoch 1, Batch 510/1563, loss: 3.783, reward: 30.510, critic_reward: 30.387, revenue_rate: 0.7520, distance: 9.4010, memory: -0.0313, power: 0.2829, lr: 0.000100, took: 177.958s
[COOPERATIVE] Epoch 1, Batch 520/1563, loss: 3.339, reward: 28.475, critic_reward: 28.394, revenue_rate: 0.6979, distance: 8.5560, memory: -0.0692, power: 0.2592, lr: 0.000100, took: 170.797s
[COOPERATIVE] Epoch 1, Batch 530/1563, loss: 9.512, reward: 24.783, critic_reward: 24.218, revenue_rate: 0.6038, distance: 7.1634, memory: -0.1099, power: 0.2167, lr: 0.000100, took: 135.188s
[COOPERATIVE] Epoch 1, Batch 540/1563, loss: 6.441, reward: 26.974, critic_reward: 26.348, revenue_rate: 0.6601, distance: 8.0273, memory: -0.0876, power: 0.2418, lr: 0.000100, took: 159.912s
[COOPERATIVE] Epoch 1, Batch 550/1563, loss: 5.764, reward: 30.054, critic_reward: 29.613, revenue_rate: 0.7378, distance: 9.1615, memory: -0.0566, power: 0.2767, lr: 0.000100, took: 174.635s
[COOPERATIVE] Epoch 1, Batch 560/1563, loss: 10.163, reward: 30.053, critic_reward: 29.913, revenue_rate: 0.7404, distance: 9.2448, memory: -0.0444, power: 0.2795, lr: 0.000100, took: 169.706s
[COOPERATIVE] Epoch 1, Batch 570/1563, loss: 4.548, reward: 31.062, critic_reward: 31.206, revenue_rate: 0.7616, distance: 9.3877, memory: -0.0403, power: 0.2861, lr: 0.000100, took: 168.620s
[COOPERATIVE] Epoch 1, Batch 580/1563, loss: 6.606, reward: 32.504, critic_reward: 33.096, revenue_rate: 0.8016, distance: 10.1411, memory: -0.0252, power: 0.3063, lr: 0.000100, took: 171.287s
[COOPERATIVE] Epoch 1, Batch 590/1563, loss: 5.321, reward: 32.684, critic_reward: 33.091, revenue_rate: 0.8072, distance: 10.2280, memory: -0.0234, power: 0.3106, lr: 0.000100, took: 171.282s
[COOPERATIVE] Epoch 1, Batch 600/1563, loss: 3.886, reward: 31.961, critic_reward: 31.352, revenue_rate: 0.7839, distance: 9.8756, memory: -0.0292, power: 0.2996, lr: 0.000100, took: 166.745s
[COOPERATIVE] Epoch 1, Batch 610/1563, loss: 6.428, reward: 31.251, critic_reward: 31.871, revenue_rate: 0.7679, distance: 9.5801, memory: -0.0463, power: 0.2908, lr: 0.000100, took: 169.416s
[COOPERATIVE] Epoch 1, Batch 620/1563, loss: 9.718, reward: 32.154, critic_reward: 32.374, revenue_rate: 0.7946, distance: 10.1488, memory: -0.0216, power: 0.3052, lr: 0.000100, took: 169.432s
[COOPERATIVE] Epoch 1, Batch 630/1563, loss: 13.161, reward: 31.787, critic_reward: 31.178, revenue_rate: 0.7839, distance: 9.9492, memory: -0.0239, power: 0.3003, lr: 0.000100, took: 169.151s
[COOPERATIVE] Epoch 1, Batch 640/1563, loss: 7.188, reward: 31.271, critic_reward: 32.650, revenue_rate: 0.7709, distance: 9.6882, memory: -0.0368, power: 0.2919, lr: 0.000100, took: 172.944s
[COOPERATIVE] Epoch 1, Batch 650/1563, loss: 6.494, reward: 31.008, critic_reward: 29.792, revenue_rate: 0.7646, distance: 9.4063, memory: -0.0398, power: 0.2865, lr: 0.000100, took: 176.228s
[COOPERATIVE] Epoch 1, Batch 660/1563, loss: 6.305, reward: 30.129, critic_reward: 30.313, revenue_rate: 0.7376, distance: 9.0265, memory: -0.0629, power: 0.2731, lr: 0.000100, took: 178.682s
[COOPERATIVE] Epoch 1, Batch 670/1563, loss: 4.387, reward: 29.972, critic_reward: 29.375, revenue_rate: 0.7342, distance: 8.9305, memory: -0.0543, power: 0.2729, lr: 0.000100, took: 175.751s
[COOPERATIVE] Epoch 1, Batch 680/1563, loss: 17.659, reward: 29.706, critic_reward: 28.487, revenue_rate: 0.7285, distance: 8.8058, memory: -0.0648, power: 0.2676, lr: 0.000100, took: 180.033s
[COOPERATIVE] Epoch 1, Batch 690/1563, loss: 2.655, reward: 30.632, critic_reward: 30.590, revenue_rate: 0.7530, distance: 9.2699, memory: -0.0425, power: 0.2809, lr: 0.000100, took: 178.608s
[COOPERATIVE] Epoch 1, Batch 700/1563, loss: 5.922, reward: 33.021, critic_reward: 32.418, revenue_rate: 0.8155, distance: 10.3877, memory: -0.0050, power: 0.3143, lr: 0.000100, took: 171.118s
[COOPERATIVE] Epoch 1, Batch 710/1563, loss: 6.633, reward: 31.430, critic_reward: 32.370, revenue_rate: 0.7738, distance: 9.5687, memory: -0.0329, power: 0.2892, lr: 0.000100, took: 174.759s
[COOPERATIVE] Epoch 1, Batch 720/1563, loss: 3.629, reward: 30.788, critic_reward: 30.835, revenue_rate: 0.7577, distance: 9.2908, memory: -0.0365, power: 0.2816, lr: 0.000100, took: 179.992s
[COOPERATIVE] Epoch 1, Batch 730/1563, loss: 7.433, reward: 30.800, critic_reward: 30.001, revenue_rate: 0.7562, distance: 9.4584, memory: -0.0467, power: 0.2853, lr: 0.000100, took: 177.130s
[COOPERATIVE] Epoch 1, Batch 740/1563, loss: 12.461, reward: 30.484, critic_reward: 31.872, revenue_rate: 0.7482, distance: 9.1834, memory: -0.0524, power: 0.2769, lr: 0.000100, took: 180.108s
[COOPERATIVE] Epoch 1, Batch 750/1563, loss: 7.934, reward: 30.677, critic_reward: 29.689, revenue_rate: 0.7515, distance: 9.2445, memory: -0.0403, power: 0.2840, lr: 0.000100, took: 180.244s
[COOPERATIVE] Epoch 1, Batch 760/1563, loss: 32.337, reward: 26.421, critic_reward: 31.496, revenue_rate: 0.6483, distance: 7.9312, memory: -0.0819, power: 0.2393, lr: 0.000100, took: 154.652s
[COOPERATIVE] Epoch 1, Batch 770/1563, loss: 41.024, reward: 28.914, critic_reward: 23.018, revenue_rate: 0.7108, distance: 8.7749, memory: -0.0571, power: 0.2657, lr: 0.000100, took: 174.959s
[COOPERATIVE] Epoch 1, Batch 780/1563, loss: 6.258, reward: 31.524, critic_reward: 31.128, revenue_rate: 0.7747, distance: 9.6993, memory: -0.0311, power: 0.2963, lr: 0.000100, took: 172.622s
[COOPERATIVE] Epoch 1, Batch 790/1563, loss: 4.489, reward: 31.091, critic_reward: 31.320, revenue_rate: 0.7620, distance: 9.4274, memory: -0.0356, power: 0.2853, lr: 0.000100, took: 177.901s
[COOPERATIVE] Epoch 1, Batch 800/1563, loss: 5.797, reward: 29.687, critic_reward: 30.093, revenue_rate: 0.7268, distance: 8.7926, memory: -0.0619, power: 0.2649, lr: 0.000100, took: 181.836s
[COOPERATIVE] Epoch 1, Batch 810/1563, loss: 4.846, reward: 29.953, critic_reward: 30.070, revenue_rate: 0.7321, distance: 8.9595, memory: -0.0536, power: 0.2689, lr: 0.000100, took: 176.323s
[COOPERATIVE] Epoch 1, Batch 820/1563, loss: 3.888, reward: 31.549, critic_reward: 31.179, revenue_rate: 0.7753, distance: 9.7412, memory: -0.0222, power: 0.2946, lr: 0.000100, took: 170.265s
[COOPERATIVE] Epoch 1, Batch 830/1563, loss: 5.533, reward: 32.609, critic_reward: 33.415, revenue_rate: 0.8035, distance: 10.1434, memory: -0.0157, power: 0.3064, lr: 0.000100, took: 172.579s
[COOPERATIVE] Epoch 1, Batch 840/1563, loss: 3.905, reward: 30.252, critic_reward: 30.835, revenue_rate: 0.7428, distance: 9.0857, memory: -0.0446, power: 0.2762, lr: 0.000100, took: 181.889s
[COOPERATIVE] Epoch 1, Batch 850/1563, loss: 5.495, reward: 30.115, critic_reward: 30.375, revenue_rate: 0.7423, distance: 9.1487, memory: -0.0455, power: 0.2741, lr: 0.000100, took: 186.163s
[COOPERATIVE] Epoch 1, Batch 860/1563, loss: 3.329, reward: 30.772, critic_reward: 30.802, revenue_rate: 0.7582, distance: 9.4037, memory: -0.0390, power: 0.2880, lr: 0.000100, took: 176.816s
[COOPERATIVE] Epoch 1, Batch 870/1563, loss: 5.665, reward: 30.356, critic_reward: 31.322, revenue_rate: 0.7460, distance: 9.3419, memory: -0.0450, power: 0.2828, lr: 0.000100, took: 178.043s
[COOPERATIVE] Epoch 1, Batch 880/1563, loss: 5.081, reward: 29.307, critic_reward: 29.778, revenue_rate: 0.7174, distance: 8.6434, memory: -0.0641, power: 0.2636, lr: 0.000100, took: 178.868s
[COOPERATIVE] Epoch 1, Batch 890/1563, loss: 4.045, reward: 31.582, critic_reward: 31.980, revenue_rate: 0.7758, distance: 9.6673, memory: -0.0472, power: 0.2909, lr: 0.000100, took: 176.793s
[COOPERATIVE] Epoch 1, Batch 900/1563, loss: 4.735, reward: 33.164, critic_reward: 33.750, revenue_rate: 0.8183, distance: 10.2964, memory: -0.0107, power: 0.3129, lr: 0.000100, took: 173.859s
[COOPERATIVE] Epoch 1, Batch 910/1563, loss: 3.942, reward: 33.595, critic_reward: 33.393, revenue_rate: 0.8298, distance: 10.5697, memory: -0.0094, power: 0.3173, lr: 0.000100, took: 175.854s
[COOPERATIVE] Epoch 1, Batch 920/1563, loss: 7.928, reward: 31.924, critic_reward: 32.724, revenue_rate: 0.7831, distance: 9.6715, memory: -0.0283, power: 0.2932, lr: 0.000100, took: 172.096s
[COOPERATIVE] Epoch 1, Batch 930/1563, loss: 10.923, reward: 30.291, critic_reward: 31.191, revenue_rate: 0.7412, distance: 9.0099, memory: -0.0571, power: 0.2714, lr: 0.000100, took: 182.636s
[COOPERATIVE] Epoch 1, Batch 940/1563, loss: 3.164, reward: 31.333, critic_reward: 31.118, revenue_rate: 0.7706, distance: 9.4743, memory: -0.0387, power: 0.2865, lr: 0.000100, took: 178.413s
[COOPERATIVE] Epoch 1, Batch 950/1563, loss: 5.258, reward: 32.071, critic_reward: 32.593, revenue_rate: 0.7890, distance: 9.8142, memory: -0.0341, power: 0.2996, lr: 0.000100, took: 168.596s
[COOPERATIVE] Epoch 1, Batch 960/1563, loss: 15.268, reward: 31.111, critic_reward: 31.972, revenue_rate: 0.7630, distance: 9.5326, memory: -0.0315, power: 0.2876, lr: 0.000100, took: 177.901s
[COOPERATIVE] Epoch 1, Batch 970/1563, loss: 3.744, reward: 28.692, critic_reward: 28.432, revenue_rate: 0.7023, distance: 8.5951, memory: -0.0578, power: 0.2573, lr: 0.000100, took: 175.834s
[COOPERATIVE] Epoch 1, Batch 980/1563, loss: 4.951, reward: 29.465, critic_reward: 29.173, revenue_rate: 0.7227, distance: 8.7232, memory: -0.0602, power: 0.2671, lr: 0.000100, took: 179.481s
[COOPERATIVE] Epoch 1, Batch 990/1563, loss: 6.054, reward: 27.912, critic_reward: 27.745, revenue_rate: 0.6856, distance: 8.3363, memory: -0.0811, power: 0.2517, lr: 0.000100, took: 171.301s
[COOPERATIVE] Epoch 1, Batch 1000/1563, loss: 2.947, reward: 29.520, critic_reward: 29.494, revenue_rate: 0.7243, distance: 8.8935, memory: -0.0576, power: 0.2675, lr: 0.000100, took: 176.207s
[COOPERATIVE] Epoch 1, Batch 1010/1563, loss: 4.692, reward: 32.607, critic_reward: 32.794, revenue_rate: 0.8043, distance: 10.1319, memory: -0.0106, power: 0.3066, lr: 0.000100, took: 172.413s
[COOPERATIVE] Epoch 1, Batch 1020/1563, loss: 4.017, reward: 34.125, critic_reward: 33.700, revenue_rate: 0.8436, distance: 10.7344, memory: 0.0026, power: 0.3250, lr: 0.000100, took: 179.470s
