多星座模式训练详细日志
================================================================================

实验时间: 2025-08-12 13:07:34
实验配置:
  问题规模: 100节点, 3卫星
  训练参数: 3轮, 批次64, 学习率0.0001
  数据规模: 训练1000, 验证100
  使用Transformer: True
  Transformer配置: 2层, 4头

COOPERATIVE 模式详细结果:
--------------------------------------------------
模型架构:
  Actor参数数量: 3,730,953
  Critic参数数量: 494,285
  总参数数量: 4,225,238
训练结果:
  最佳验证奖励: 24.272480
测试性能:
  平均收益率: 0.605368
  平均距离: 7.652338
  平均内存使用: -0.106954
  平均功耗: 0.237606
  综合性能评分: 2.055234
文件路径:
  模型保存: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_12_12_38_27
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_cooperative/

COMPETITIVE 模式详细结果:
--------------------------------------------------
模型架构:
  Actor参数数量: 3,730,953
  Critic参数数量: 494,285
  总参数数量: 4,225,238
训练结果:
  最佳验证奖励: 27.807296
测试性能:
  平均收益率: 0.672786
  平均距离: 8.662099
  平均内存使用: -0.075935
  平均功耗: 0.265590
  综合性能评分: 2.226051
文件路径:
  模型保存: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_12_12_45_19
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_competitive/

HYBRID 模式详细结果:
--------------------------------------------------
模型架构:
  Actor参数数量: 3,927,817
  Critic参数数量: 691,149
  总参数数量: 4,618,966
训练结果:
  最佳验证奖励: 25.031712
测试性能:
  平均收益率: 0.651516
  平均距离: 8.522649
  平均内存使用: -0.073542
  平均功耗: 0.259688
  综合性能评分: 2.087220
文件路径:
  模型保存: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_12_12_56_17
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_hybrid/

对比分析:
==================================================
最佳奖励模式: competitive (27.8073)
最佳收益率模式: competitive (0.6728)
最短距离模式: cooperative (7.6523)
最低功耗模式: cooperative (0.2376)

推荐使用: competitive 模式
推荐理由: 在关键性能指标上表现最佳
