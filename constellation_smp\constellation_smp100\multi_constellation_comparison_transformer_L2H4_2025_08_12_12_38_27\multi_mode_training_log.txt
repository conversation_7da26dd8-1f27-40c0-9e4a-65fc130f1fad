多星座模式训练实验
================================================================================
实验时间: 2025_08_12_12_38_27
设备: cuda
问题规模: 100节点, 3卫星
训练配置: 3轮, 批次大小64
使用Transformer: True
Transformer配置: 2层, 4头

================================================================================
开始训练星座模式: COOPERATIVE
================================================================================
cooperative 模式模型信息:
  Actor参数数量: 3,730,953
  Critic参数数量: 494,285
  总参数数量: 4,225,238
详细训练配置:
  数据集大小: 训练1000, 验证100
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 cooperative 模式...
训练开始时间: 2025-08-12 12:38:31
详细训练过程:
[COOPERATIVE] 开始训练 Epoch 1/3
[COOPERATIVE] Epoch 1, Batch 10/16, loss: 556.241, reward: 10.293, critic_reward: 5.892, revenue_rate: 0.2573, distance: 3.6988, memory: -0.0926, power: 0.1130, lr: 0.000100, took: 42.013s
[COOPERATIVE] 开始验证...
[COOPERATIVE] 验证完成 - Epoch 1, reward: 13.002, revenue_rate: 0.3211, distance: 4.3406, memory: -0.1976, power: 0.1305
[COOPERATIVE] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_12_12_38_27 (验证集奖励: 13.0022)
[COOPERATIVE] 开始训练 Epoch 2/3
[COOPERATIVE] Epoch 2, Batch 10/16, loss: 24.690, reward: 14.821, critic_reward: 12.831, revenue_rate: 0.3650, distance: 4.7808, memory: -0.1778, power: 0.1451, lr: 0.000100, took: 62.823s
[COOPERATIVE] 开始验证...
[COOPERATIVE] 验证完成 - Epoch 2, reward: 19.550, revenue_rate: 0.4832, distance: 6.3480, memory: -0.1275, power: 0.1904
[COOPERATIVE] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_12_12_38_27 (验证集奖励: 19.5495)
[COOPERATIVE] 开始训练 Epoch 3/3
[COOPERATIVE] Epoch 3, Batch 10/16, loss: 43.684, reward: 19.966, critic_reward: 18.961, revenue_rate: 0.4906, distance: 6.3359, memory: -0.1419, power: 0.1907, lr: 0.000100, took: 101.023s
[COOPERATIVE] 开始验证...
[COOPERATIVE] 验证完成 - Epoch 3, reward: 24.272, revenue_rate: 0.5975, distance: 7.6185, memory: -0.0984, power: 0.2349
[COOPERATIVE] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_12_12_38_27 (验证集奖励: 24.2725)
[COOPERATIVE] 训练完成
训练结束时间: 2025-08-12 12:44:59
训练总耗时: 0:06:28.715480
训练过程统计:
  最终训练奖励: 21.9893
  最佳验证奖励: 24.2725
  训练轮数完成: 48
  奖励提升: 12.2290
  平均每轮提升: 0.2548
生成训练曲线图...
✓ 训练曲线图已保存到: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_12_12_38_27\train_loss_reward.png
开始测试 cooperative 模式...
测试配置:
  测试数据大小: 100
  测试批次数: 2
  可视化样本数: 5
测试开始时间: 2025-08-12 12:45:04
测试结束时间: 2025-08-12 12:45:19
测试耗时: 0:00:14.601287

COOPERATIVE 模式完整结果:
==================================================
训练结果:
  最佳验证奖励: 24.2725
  模型保存路径: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_12_12_38_27
测试结果:
  平均收益率: 0.6054
  平均距离: 7.6523
  平均内存使用: -0.1070
  平均功耗: 0.2376
模型信息:
  Actor参数: 3,730,953
  Critic参数: 494,285
  总参数: 4,225,238
综合性能评分: 2.0552
文件输出:
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_cooperative/
==================================================

================================================================================
开始训练星座模式: COMPETITIVE
================================================================================
competitive 模式模型信息:
  Actor参数数量: 3,730,953
  Critic参数数量: 494,285
  总参数数量: 4,225,238
详细训练配置:
  数据集大小: 训练1000, 验证100
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 competitive 模式...
训练开始时间: 2025-08-12 12:45:23
详细训练过程:
[COMPETITIVE] 开始训练 Epoch 1/3
[COMPETITIVE] Epoch 1, Batch 10/16, loss: 860.365, reward: 15.050, critic_reward: 5.802, revenue_rate: 0.3793, distance: 5.6645, memory: -0.0585, power: 0.1705, lr: 0.000100, took: 85.102s
[COMPETITIVE] 开始验证...
[COMPETITIVE] 验证完成 - Epoch 1, reward: 23.011, revenue_rate: 0.5766, distance: 8.1340, memory: -0.0751, power: 0.2515
[COMPETITIVE] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_12_12_45_19 (验证集奖励: 23.0108)
[COMPETITIVE] 开始训练 Epoch 2/3
[COMPETITIVE] Epoch 2, Batch 10/16, loss: 43.660, reward: 23.105, critic_reward: 25.370, revenue_rate: 0.5777, distance: 8.0878, memory: -0.0891, power: 0.2435, lr: 0.000100, took: 123.203s
[COMPETITIVE] 开始验证...
[COMPETITIVE] 验证完成 - Epoch 2, reward: 27.267, revenue_rate: 0.6778, distance: 9.1116, memory: -0.0522, power: 0.2801
[COMPETITIVE] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_12_12_45_19 (验证集奖励: 27.2673)
[COMPETITIVE] 开始训练 Epoch 3/3
[COMPETITIVE] Epoch 3, Batch 10/16, loss: 88.635, reward: 25.345, critic_reward: 31.731, revenue_rate: 0.6264, distance: 8.3041, memory: -0.0886, power: 0.2526, lr: 0.000100, took: 152.297s
[COMPETITIVE] 开始验证...
[COMPETITIVE] 验证完成 - Epoch 3, reward: 27.807, revenue_rate: 0.6871, distance: 8.9692, memory: -0.0739, power: 0.2741
[COMPETITIVE] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_12_12_45_19 (验证集奖励: 27.8073)
[COMPETITIVE] 训练完成
训练结束时间: 2025-08-12 12:55:57
训练总耗时: 0:10:33.855696
训练过程统计:
  最终训练奖励: 26.2811
  最佳验证奖励: 27.8073
  训练轮数完成: 48
  奖励提升: 16.8746
  平均每轮提升: 0.3516
生成训练曲线图...
✓ 训练曲线图已保存到: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_12_12_45_19\train_loss_reward.png
开始测试 competitive 模式...
测试配置:
  测试数据大小: 100
  测试批次数: 2
  可视化样本数: 5
测试开始时间: 2025-08-12 12:56:02
测试结束时间: 2025-08-12 12:56:17
测试耗时: 0:00:15.324897

COMPETITIVE 模式完整结果:
==================================================
训练结果:
  最佳验证奖励: 27.8073
  模型保存路径: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_12_12_45_19
测试结果:
  平均收益率: 0.6728
  平均距离: 8.6621
  平均内存使用: -0.0759
  平均功耗: 0.2656
模型信息:
  Actor参数: 3,730,953
  Critic参数: 494,285
  总参数: 4,225,238
综合性能评分: 2.2261
文件输出:
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_competitive/
==================================================

================================================================================
开始训练星座模式: HYBRID
================================================================================
hybrid 模式模型信息:
  Actor参数数量: 3,927,817
  Critic参数数量: 691,149
  总参数数量: 4,618,966
详细训练配置:
  数据集大小: 训练1000, 验证100
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 hybrid 模式...
训练开始时间: 2025-08-12 12:56:21
详细训练过程:
[HYBRID] 开始训练 Epoch 1/3
[HYBRID] Epoch 1, Batch 10/16, loss: 657.721, reward: 14.937, critic_reward: 17.365, revenue_rate: 0.3732, distance: 5.3089, memory: -0.1246, power: 0.1605, lr: 0.000100, took: 98.354s
[HYBRID] 开始验证...
[HYBRID] 验证完成 - Epoch 1, reward: 19.483, revenue_rate: 0.4875, distance: 6.9098, memory: -0.1269, power: 0.2083
[HYBRID] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_12_12_56_17 (验证集奖励: 19.4834)
[HYBRID] 开始训练 Epoch 2/3
[HYBRID] Epoch 2, Batch 10/16, loss: 111.388, reward: 21.239, critic_reward: 24.230, revenue_rate: 0.5284, distance: 7.2965, memory: -0.1130, power: 0.2207, lr: 0.000100, took: 124.674s
[HYBRID] 开始验证...
[HYBRID] 验证完成 - Epoch 2, reward: 23.557, revenue_rate: 0.5886, distance: 8.1723, memory: -0.0659, power: 0.2463
[HYBRID] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_12_12_56_17 (验证集奖励: 23.5567)
[HYBRID] 开始训练 Epoch 3/3
[HYBRID] Epoch 3, Batch 10/16, loss: 61.486, reward: 23.221, critic_reward: 24.511, revenue_rate: 0.5754, distance: 7.7388, memory: -0.1085, power: 0.2343, lr: 0.000100, took: 153.461s
[HYBRID] 开始验证...
[HYBRID] 验证完成 - Epoch 3, reward: 25.032, revenue_rate: 0.6177, distance: 7.9666, memory: -0.0797, power: 0.2437
[HYBRID] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_12_12_56_17 (验证集奖励: 25.0317)
[HYBRID] 训练完成
训练结束时间: 2025-08-12 13:07:09
训练总耗时: 0:10:47.546090
训练过程统计:
  最终训练奖励: 24.5452
  最佳验证奖励: 25.0317
  训练轮数完成: 48
  奖励提升: 14.1902
  平均每轮提升: 0.2956
生成训练曲线图...
✓ 训练曲线图已保存到: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_12_12_56_17\train_loss_reward.png
开始测试 hybrid 模式...
测试配置:
  测试数据大小: 100
  测试批次数: 2
  可视化样本数: 5
测试开始时间: 2025-08-12 13:07:14
测试结束时间: 2025-08-12 13:07:29
测试耗时: 0:00:15.452017

HYBRID 模式完整结果:
==================================================
训练结果:
  最佳验证奖励: 25.0317
  模型保存路径: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_12_12_56_17
测试结果:
  平均收益率: 0.6515
  平均距离: 8.5226
  平均内存使用: -0.0735
  平均功耗: 0.2597
模型信息:
  Actor参数: 3,927,817
  Critic参数: 691,149
  总参数: 4,618,966
综合性能评分: 2.0872
文件输出:
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_hybrid/
==================================================

================================================================================
生成对比分析
================================================================================
生成多模式训练曲线对比图...
✓ 多模式训练曲线图已保存

创建对比图表...
对比图表已保存到: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\comparison_results
对比结果已保存到:
  JSON文件: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\comparison_results\comparison_results.json
  文本报告: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\comparison_results\comparison_report.txt
详细训练日志已保存到: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\detailed_training_summary.txt

================================================================================
多星座模式训练实验总结
================================================================================
实验总耗时: 0:29:07.484758
成功训练模式数: 3/3

各模式详细对比:
模式           奖励       收益率      距离       内存       功耗       参数数       
----------------------------------------------------------------------
cooperative  24.2725  0.6054   7.6523   -0.1070  0.2376   4,225,238 
competitive  27.8073  0.6728   8.6621   -0.0759  0.2656   4,225,238 
hybrid       25.0317  0.6515   8.5226   -0.0735  0.2597   4,618,966 

性能排名:
🏆 最高奖励: COMPETITIVE (27.8073)
💰 最高收益率: COMPETITIVE (0.6728)
🚀 最短距离: COOPERATIVE (7.6523)
⚡ 最低功耗: COOPERATIVE (0.2376)

💡 推荐模式: COMPETITIVE
   理由: 在奖励和收益率两个关键指标上都表现最佳

📁 实验结果文件:
   主目录: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27
   对比分析: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\comparison_results
   全局日志: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\multi_mode_training_log.txt
   cooperative 模式: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_12_12_38_27
   competitive 模式: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_12_12_45_19
   hybrid 模式: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_12_12_38_27\constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_12_12_56_17
