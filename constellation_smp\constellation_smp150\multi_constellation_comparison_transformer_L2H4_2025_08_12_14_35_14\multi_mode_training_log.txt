多星座模式训练实验
================================================================================
实验时间: 2025_08_12_14_35_14
设备: cuda
问题规模: 150节点, 3卫星
训练配置: 3轮, 批次大小64
使用Transformer: True
Transformer配置: 2层, 4头

================================================================================
开始训练星座模式: COOPERATIVE
================================================================================
cooperative 模式模型信息:
  Actor参数数量: 3,730,953
  Critic参数数量: 494,285
  总参数数量: 4,225,238
详细训练配置:
  数据集大小: 训练1000, 验证100
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 cooperative 模式...
训练开始时间: 2025-08-12 14:35:20
详细训练过程:
[COOPERATIVE] 开始训练 Epoch 1/3
[COOPERATIVE] Epoch 1, Batch 10/16, loss: 1259.327, reward: 13.744, critic_reward: 8.439, revenue_rate: 0.2365, distance: 5.3881, memory: -0.0004, power: 0.1616, lr: 0.000100, took: 133.621s
[COOPERATIVE] 开始验证...
[COOPERATIVE] 验证完成 - Epoch 1, reward: 22.441, revenue_rate: 0.3801, distance: 8.0983, memory: -0.0628, power: 0.2419
[COOPERATIVE] 已保存新模型到 constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_12_14_35_14 (验证集奖励: 22.4413)
[COOPERATIVE] 开始训练 Epoch 2/3
[COOPERATIVE] Epoch 2, Batch 10/16, loss: 30.484, reward: 25.103, critic_reward: 26.118, revenue_rate: 0.4242, distance: 8.7229, memory: -0.0542, power: 0.2646, lr: 0.000100, took: 228.984s
[COOPERATIVE] 开始验证...
[COOPERATIVE] 验证完成 - Epoch 2, reward: 29.732, revenue_rate: 0.4992, distance: 10.0381, memory: -0.0118, power: 0.3022
[COOPERATIVE] 已保存新模型到 constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_12_14_35_14 (验证集奖励: 29.7317)
[COOPERATIVE] 开始训练 Epoch 3/3
[COOPERATIVE] Epoch 3, Batch 10/16, loss: 19.588, reward: 30.542, critic_reward: 28.779, revenue_rate: 0.5132, distance: 10.1294, memory: -0.0222, power: 0.3078, lr: 0.000100, took: 268.260s
[COOPERATIVE] 开始验证...
[COOPERATIVE] 验证完成 - Epoch 3, reward: 37.009, revenue_rate: 0.6182, distance: 12.0654, memory: 0.0246, power: 0.3629
[COOPERATIVE] 已保存新模型到 constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_12_14_35_14 (验证集奖励: 37.0086)
[COOPERATIVE] 训练完成
训练结束时间: 2025-08-12 14:53:40
训练总耗时: 0:18:20.286313
训练过程统计:
  最终训练奖励: 29.1038
  最佳验证奖励: 37.0086
  训练轮数完成: 48
  奖励提升: 17.0338
  平均每轮提升: 0.3549
生成训练曲线图...
✓ 训练曲线图已保存到: constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_12_14_35_14\train_loss_reward.png
开始测试 cooperative 模式...
测试配置:
  测试数据大小: 100
  测试批次数: 2
  可视化样本数: 5
测试开始时间: 2025-08-12 14:53:46
测试结束时间: 2025-08-12 14:54:18
测试耗时: 0:00:32.078316

COOPERATIVE 模式完整结果:
==================================================
训练结果:
  最佳验证奖励: 37.0086
  模型保存路径: constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_12_14_35_14
测试结果:
  平均收益率: 0.5656
  平均距离: 10.8491
  平均内存使用: 0.0015
  平均功耗: 0.3276
模型信息:
  Actor参数: 3,730,953
  Critic参数: 494,285
  总参数: 4,225,238
综合性能评分: 0.0672
文件输出:
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_cooperative/
==================================================

================================================================================
开始训练星座模式: COMPETITIVE
================================================================================
competitive 模式模型信息:
  Actor参数数量: 3,730,953
  Critic参数数量: 494,285
  总参数数量: 4,225,238
详细训练配置:
  数据集大小: 训练1000, 验证100
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 competitive 模式...
训练开始时间: 2025-08-12 14:54:24
详细训练过程:
[COMPETITIVE] 开始训练 Epoch 1/3
[COMPETITIVE] Epoch 1, Batch 10/16, loss: 1918.106, reward: 14.363, critic_reward: -6.917, revenue_rate: 0.2451, distance: 5.3777, memory: -0.0433, power: 0.1631, lr: 0.000100, took: 220.926s
[COMPETITIVE] 开始验证...
[COMPETITIVE] 验证完成 - Epoch 1, reward: 22.281, revenue_rate: 0.3759, distance: 7.8589, memory: -0.0792, power: 0.2420
[COMPETITIVE] 已保存新模型到 constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_12_14_54_18 (验证集奖励: 22.2812)
[COMPETITIVE] 开始训练 Epoch 2/3
[COMPETITIVE] Epoch 2, Batch 10/16, loss: 17.981, reward: 25.590, critic_reward: 25.942, revenue_rate: 0.4321, distance: 8.8574, memory: -0.0549, power: 0.2699, lr: 0.000100, took: 357.190s
[COMPETITIVE] 开始验证...
[COMPETITIVE] 验证完成 - Epoch 2, reward: 31.500, revenue_rate: 0.5279, distance: 10.5366, memory: -0.0105, power: 0.3239
[COMPETITIVE] 已保存新模型到 constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_12_14_54_18 (验证集奖励: 31.5000)
[COMPETITIVE] 开始训练 Epoch 3/3
[COMPETITIVE] Epoch 3, Batch 10/16, loss: 31.315, reward: 31.635, critic_reward: 33.132, revenue_rate: 0.5313, distance: 10.4346, memory: -0.0102, power: 0.3166, lr: 0.000100, took: 325.021s
[COMPETITIVE] 开始验证...
[COMPETITIVE] 验证完成 - Epoch 3, reward: 33.470, revenue_rate: 0.5558, distance: 10.4867, memory: -0.0188, power: 0.3130
[COMPETITIVE] 已保存新模型到 constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_12_14_54_18 (验证集奖励: 33.4695)
[COMPETITIVE] 训练完成
训练结束时间: 2025-08-12 15:20:12
训练总耗时: 0:25:48.602607
训练过程统计:
  最终训练奖励: 34.3649
  最佳验证奖励: 33.4695
  训练轮数完成: 48
  奖励提升: 22.6918
  平均每轮提升: 0.4727
生成训练曲线图...
✓ 训练曲线图已保存到: constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_12_14_54_18\train_loss_reward.png
开始测试 competitive 模式...
测试配置:
  测试数据大小: 100
  测试批次数: 2
  可视化样本数: 5
测试开始时间: 2025-08-12 15:20:18
测试结束时间: 2025-08-12 15:20:46
测试耗时: 0:00:28.001916

COMPETITIVE 模式完整结果:
==================================================
训练结果:
  最佳验证奖励: 33.4695
  模型保存路径: constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_12_14_54_18
测试结果:
  平均收益率: 0.5588
  平均距离: 10.4677
  平均内存使用: -0.0056
  平均功耗: 0.3215
模型信息:
  Actor参数: 3,730,953
  Critic参数: 494,285
  总参数: 4,225,238
综合性能评分: 0.1909
文件输出:
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_competitive/
==================================================

================================================================================
开始训练星座模式: HYBRID
================================================================================
hybrid 模式模型信息:
  Actor参数数量: 3,927,817
  Critic参数数量: 691,149
  总参数数量: 4,618,966
详细训练配置:
  数据集大小: 训练1000, 验证100
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 hybrid 模式...
训练开始时间: 2025-08-12 15:20:52
详细训练过程:
[HYBRID] 开始训练 Epoch 1/3
[HYBRID] Epoch 1, Batch 10/16, loss: 1570.287, reward: 20.020, critic_reward: 25.356, revenue_rate: 0.3421, distance: 7.5271, memory: -0.0253, power: 0.2303, lr: 0.000100, took: 243.049s
[HYBRID] 开始验证...
[HYBRID] 验证完成 - Epoch 1, reward: 25.658, revenue_rate: 0.4364, distance: 9.4581, memory: -0.0317, power: 0.2845
[HYBRID] 已保存新模型到 constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_12_15_20_46 (验证集奖励: 25.6576)
[HYBRID] 开始训练 Epoch 2/3
[HYBRID] Epoch 2, Batch 10/16, loss: 83.691, reward: 26.860, critic_reward: 25.059, revenue_rate: 0.4544, distance: 9.4372, memory: -0.0362, power: 0.2841, lr: 0.000100, took: 275.198s
[HYBRID] 开始验证...
[HYBRID] 验证完成 - Epoch 2, reward: 30.229, revenue_rate: 0.5055, distance: 9.9589, memory: -0.0207, power: 0.2963
[HYBRID] 已保存新模型到 constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_12_15_20_46 (验证集奖励: 30.2288)
[HYBRID] 开始训练 Epoch 3/3
[HYBRID] Epoch 3, Batch 10/16, loss: 50.524, reward: 30.021, critic_reward: 30.727, revenue_rate: 0.5022, distance: 9.7305, memory: -0.0289, power: 0.2951, lr: 0.000100, took: 283.859s
[HYBRID] 开始验证...
[HYBRID] 验证完成 - Epoch 3, reward: 35.771, revenue_rate: 0.5955, distance: 11.3894, memory: 0.0203, power: 0.3405
[HYBRID] 已保存新模型到 constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_12_15_20_46 (验证集奖励: 35.7709)
[HYBRID] 训练完成
训练结束时间: 2025-08-12 15:43:37
训练总耗时: 0:22:45.815113
训练过程统计:
  最终训练奖励: 32.8726
  最佳验证奖励: 35.7709
  训练轮数完成: 48
  奖励提升: 19.1632
  平均每轮提升: 0.3992
生成训练曲线图...
✓ 训练曲线图已保存到: constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_12_15_20_46\train_loss_reward.png
开始测试 hybrid 模式...
测试配置:
  测试数据大小: 100
  测试批次数: 2
  可视化样本数: 5
测试开始时间: 2025-08-12 15:43:43
测试结束时间: 2025-08-12 15:44:12
测试耗时: 0:00:29.475350

HYBRID 模式完整结果:
==================================================
训练结果:
  最佳验证奖励: 35.7709
  模型保存路径: constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_12_15_20_46
测试结果:
  平均收益率: 0.5909
  平均距离: 11.3551
  平均内存使用: 0.0204
  平均功耗: 0.3390
模型信息:
  Actor参数: 3,927,817
  Critic参数: 691,149
  总参数: 4,618,966
综合性能评分: 0.0519
文件输出:
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_hybrid/
==================================================

================================================================================
生成对比分析
================================================================================
生成多模式训练曲线对比图...
✓ 多模式训练曲线图已保存

创建对比图表...
对比图表已保存到: constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\comparison_results
对比结果已保存到:
  JSON文件: constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\comparison_results\comparison_results.json
  文本报告: constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\comparison_results\comparison_report.txt
详细训练日志已保存到: constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\detailed_training_summary.txt

================================================================================
多星座模式训练实验总结
================================================================================
实验总耗时: 1:09:03.623031
成功训练模式数: 3/3

各模式详细对比:
模式           奖励       收益率      距离       内存       功耗       参数数       
----------------------------------------------------------------------
cooperative  37.0086  0.5656   10.8491  0.0015   0.3276   4,225,238 
competitive  33.4695  0.5588   10.4677  -0.0056  0.3215   4,225,238 
hybrid       35.7709  0.5909   11.3551  0.0204   0.3390   4,618,966 

性能排名:
🏆 最高奖励: COOPERATIVE (37.0086)
💰 最高收益率: HYBRID (0.5909)
🚀 最短距离: COMPETITIVE (10.4677)
⚡ 最低功耗: COMPETITIVE (0.3215)

💡 推荐模式分析:
   如果追求最高奖励: COOPERATIVE
   如果追求最高收益率: HYBRID

📁 实验结果文件:
   主目录: constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14
   对比分析: constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\comparison_results
   全局日志: constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\multi_mode_training_log.txt
   cooperative 模式: constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_12_14_35_14
   competitive 模式: constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_12_14_54_18
   hybrid 模式: constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_12_15_20_46
