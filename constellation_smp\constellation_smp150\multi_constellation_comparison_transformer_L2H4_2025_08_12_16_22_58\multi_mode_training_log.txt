多星座模式训练实验
================================================================================
实验时间: 2025_08_12_16_22_58
设备: cuda
问题规模: 150节点, 3卫星
训练配置: 3轮, 批次大小64
使用Transformer: True
Transformer配置: 2层, 4头

================================================================================
开始训练星座模式: COOPERATIVE
================================================================================
cooperative 模式模型信息:
  Actor参数数量: 3,730,953
  Critic参数数量: 494,285
  总参数数量: 4,225,238
详细训练配置:
  数据集大小: 训练100000, 验证10000
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 cooperative 模式...
训练开始时间: 2025-08-12 16:32:03
详细训练过程:
[COOPERATIVE] 开始训练 Epoch 1/3
[COOPERATIVE] Epoch 1, Batch 10/1563, loss: 1208.672, reward: 12.230, critic_reward: 8.541, revenue_rate: 0.2099, distance: 4.5950, memory: -0.0516, power: 0.1405, lr: 0.000100, took: 116.695s
[COOPERATIVE] Epoch 1, Batch 20/1563, loss: 88.223, reward: 19.724, critic_reward: 22.737, revenue_rate: 0.3331, distance: 6.8598, memory: -0.1155, power: 0.2080, lr: 0.000100, took: 171.609s
[COOPERATIVE] Epoch 1, Batch 30/1563, loss: 24.638, reward: 23.761, critic_reward: 24.601, revenue_rate: 0.4023, distance: 8.2179, memory: -0.0850, power: 0.2453, lr: 0.000100, took: 203.753s
[COOPERATIVE] Epoch 1, Batch 40/1563, loss: 13.926, reward: 31.038, critic_reward: 30.649, revenue_rate: 0.5247, distance: 10.5717, memory: -0.0057, power: 0.3170, lr: 0.000100, took: 252.570s
[COOPERATIVE] Epoch 1, Batch 50/1563, loss: 16.408, reward: 35.114, critic_reward: 33.808, revenue_rate: 0.5940, distance: 12.1997, memory: 0.0797, power: 0.3682, lr: 0.000100, took: 319.670s
[COOPERATIVE] Epoch 1, Batch 60/1563, loss: 50.560, reward: 34.474, critic_reward: 36.162, revenue_rate: 0.5821, distance: 11.5894, memory: 0.0220, power: 0.3477, lr: 0.000100, took: 308.738s
[COOPERATIVE] Epoch 1, Batch 70/1563, loss: 41.468, reward: 33.029, critic_reward: 32.289, revenue_rate: 0.5524, distance: 10.5911, memory: 0.0032, power: 0.3197, lr: 0.000100, took: 272.491s
[COOPERATIVE] Epoch 1, Batch 80/1563, loss: 27.464, reward: 33.515, critic_reward: 31.294, revenue_rate: 0.5614, distance: 10.6296, memory: 0.0027, power: 0.3200, lr: 0.000100, took: 272.382s
[COOPERATIVE] Epoch 1, Batch 90/1563, loss: 19.186, reward: 34.913, critic_reward: 36.084, revenue_rate: 0.5843, distance: 11.0809, memory: 0.0041, power: 0.3364, lr: 0.000100, took: 295.777s
[COOPERATIVE] Epoch 1, Batch 100/1563, loss: 7.656, reward: 36.129, critic_reward: 35.814, revenue_rate: 0.6026, distance: 11.3151, memory: 0.0162, power: 0.3419, lr: 0.000100, took: 305.149s
[COOPERATIVE] Epoch 1, Batch 110/1563, loss: 10.141, reward: 37.112, critic_reward: 37.864, revenue_rate: 0.6199, distance: 11.7456, memory: 0.0320, power: 0.3547, lr: 0.000100, took: 315.613s
❌ cooperative 模式训练失败: CUDA out of memory. Tried to allocate 30.00 MiB. GPU 0 has a total capacity of 6.00 GiB of which 0 bytes is free. Of the allocated memory 19.68 GiB is allocated by PyTorch, and 900.79 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0811single_smp\train_multi_constellation_modes.py", line 832, in main
    result = train_single_mode(mode, base_save_dir, transformer_config)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0811single_smp\train_multi_constellation_modes.py", line 348, in train_single_mode
    best_reward, training_stats = train_constellation_with_detailed_logging(
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0811single_smp\train_multi_constellation_modes.py", line 141, in train_constellation_with_detailed_logging
    critic_est = critic(static, dynamic).view(-1)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0811single_smp\constellation_smp\gpn_constellation.py", line 421, in forward
    constellation_features, _ = self.constellation_encoder(static, dynamic)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0811single_smp\constellation_smp\gpn_constellation.py", line 110, in forward
    sat_features_attn = self.apply_attention(satellite_features_stack, batch_size, seq_len)
  File "c:\Users\<USER>\Desktop\新建文件夹 (2)\卫星星座\0811single_smp\constellation_smp\gpn_constellation.py", line 186, in apply_attention
    features_attn, _ = self.inter_satellite_attention(
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\modules\activation.py", line 1368, in forward
    attn_output, attn_output_weights = F.multi_head_attention_forward(
  File "C:\Users\<USER>\.conda\envs\py310\lib\site-packages\torch\nn\functional.py", line 6249, in multi_head_attention_forward
    attn_output.transpose(0, 1).contiguous().view(tgt_len * bsz, embed_dim)
torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 30.00 MiB. GPU 0 has a total capacity of 6.00 GiB of which 0 bytes is free. Of the allocated memory 19.68 GiB is allocated by PyTorch, and 900.79 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)


================================================================================
开始训练星座模式: COMPETITIVE
================================================================================
competitive 模式模型信息:
  Actor参数数量: 3,730,953
  Critic参数数量: 494,285
  总参数数量: 4,225,238
详细训练配置:
  数据集大小: 训练100000, 验证10000
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 competitive 模式...
训练开始时间: 2025-08-12 17:34:15
详细训练过程:
[COMPETITIVE] 开始训练 Epoch 1/3
[COMPETITIVE] Epoch 1, Batch 10/1563, loss: 1832.336, reward: 10.698, critic_reward: -6.878, revenue_rate: 0.1861, distance: 4.2864, memory: 0.0169, power: 0.1301, lr: 0.000100, took: 158.171s
[COMPETITIVE] Epoch 1, Batch 20/1563, loss: 76.065, reward: 15.507, critic_reward: 17.834, revenue_rate: 0.2622, distance: 5.4847, memory: -0.1338, power: 0.1664, lr: 0.000100, took: 189.280s
[COMPETITIVE] Epoch 1, Batch 30/1563, loss: 17.047, reward: 18.401, critic_reward: 17.052, revenue_rate: 0.3114, distance: 6.3990, memory: -0.1270, power: 0.1930, lr: 0.000100, took: 210.516s
[COMPETITIVE] Epoch 1, Batch 40/1563, loss: 38.585, reward: 21.583, critic_reward: 22.883, revenue_rate: 0.3637, distance: 7.2932, memory: -0.0982, power: 0.2189, lr: 0.000100, took: 232.136s
[COMPETITIVE] Epoch 1, Batch 50/1563, loss: 24.665, reward: 30.136, critic_reward: 31.562, revenue_rate: 0.5068, distance: 10.1356, memory: -0.0176, power: 0.3056, lr: 0.000100, took: 289.043s
[COMPETITIVE] Epoch 1, Batch 60/1563, loss: 53.476, reward: 33.119, critic_reward: 29.323, revenue_rate: 0.5590, distance: 11.1270, memory: 0.0242, power: 0.3358, lr: 0.000100, took: 323.173s
[COMPETITIVE] Epoch 1, Batch 70/1563, loss: 19.289, reward: 34.482, critic_reward: 36.157, revenue_rate: 0.5781, distance: 11.2454, memory: 0.0098, power: 0.3360, lr: 0.000100, took: 315.538s
[COMPETITIVE] Epoch 1, Batch 80/1563, loss: 6.139, reward: 31.614, critic_reward: 31.776, revenue_rate: 0.5275, distance: 9.7846, memory: -0.0309, power: 0.2963, lr: 0.000100, took: 296.014s
[COMPETITIVE] Epoch 1, Batch 90/1563, loss: 17.533, reward: 35.537, critic_reward: 37.533, revenue_rate: 0.5936, distance: 11.1272, memory: 0.0105, power: 0.3391, lr: 0.000100, took: 313.109s
[COMPETITIVE] Epoch 1, Batch 100/1563, loss: 11.084, reward: 37.135, critic_reward: 36.823, revenue_rate: 0.6204, distance: 11.7727, memory: 0.0245, power: 0.3522, lr: 0.000100, took: 344.544s
[COMPETITIVE] Epoch 1, Batch 110/1563, loss: 9.338, reward: 36.882, critic_reward: 37.255, revenue_rate: 0.6137, distance: 11.3721, memory: 0.0162, power: 0.3426, lr: 0.000100, took: 318.127s
[COMPETITIVE] Epoch 1, Batch 120/1563, loss: 8.695, reward: 39.067, critic_reward: 39.594, revenue_rate: 0.6520, distance: 12.2060, memory: 0.0432, power: 0.3717, lr: 0.000100, took: 362.577s
[COMPETITIVE] Epoch 1, Batch 130/1563, loss: 11.760, reward: 39.097, critic_reward: 40.274, revenue_rate: 0.6523, distance: 12.2884, memory: 0.0463, power: 0.3723, lr: 0.000100, took: 364.183s
[COMPETITIVE] Epoch 1, Batch 140/1563, loss: 12.083, reward: 41.003, critic_reward: 40.439, revenue_rate: 0.6853, distance: 13.0179, memory: 0.0584, power: 0.3946, lr: 0.000100, took: 385.098s
[COMPETITIVE] Epoch 1, Batch 150/1563, loss: 8.321, reward: 41.250, critic_reward: 40.867, revenue_rate: 0.6910, distance: 13.2870, memory: 0.0820, power: 0.4009, lr: 0.000100, took: 388.658s
[COMPETITIVE] Epoch 1, Batch 160/1563, loss: 8.363, reward: 38.838, critic_reward: 38.624, revenue_rate: 0.6490, distance: 12.2036, memory: 0.0499, power: 0.3710, lr: 0.000100, took: 368.568s
[COMPETITIVE] Epoch 1, Batch 170/1563, loss: 23.176, reward: 35.997, critic_reward: 33.797, revenue_rate: 0.6010, distance: 11.0406, memory: 0.0162, power: 0.3344, lr: 0.000100, took: 314.177s
[COMPETITIVE] Epoch 1, Batch 180/1563, loss: 18.652, reward: 37.017, critic_reward: 36.098, revenue_rate: 0.6181, distance: 11.3404, memory: 0.0155, power: 0.3393, lr: 0.000100, took: 373.051s
[COMPETITIVE] Epoch 1, Batch 190/1563, loss: 37.683, reward: 39.485, critic_reward: 42.590, revenue_rate: 0.6596, distance: 12.3291, memory: 0.0520, power: 0.3720, lr: 0.000100, took: 345.479s
[COMPETITIVE] Epoch 1, Batch 200/1563, loss: 18.244, reward: 39.331, critic_reward: 40.291, revenue_rate: 0.6544, distance: 12.1110, memory: 0.0387, power: 0.3652, lr: 0.000100, took: 313.970s
[COMPETITIVE] Epoch 1, Batch 210/1563, loss: 8.630, reward: 40.445, critic_reward: 40.835, revenue_rate: 0.6768, distance: 12.7030, memory: 0.0674, power: 0.3842, lr: 0.000100, took: 418.195s
[COMPETITIVE] Epoch 1, Batch 220/1563, loss: 8.051, reward: 42.512, critic_reward: 42.900, revenue_rate: 0.7138, distance: 13.5041, memory: 0.0804, power: 0.4077, lr: 0.000100, took: 456.496s
