多星座模式训练详细日志
================================================================================

实验时间: 2025-08-12 15:44:17
实验配置:
  问题规模: 150节点, 3卫星
  训练参数: 3轮, 批次64, 学习率0.0001
  数据规模: 训练1000, 验证100
  使用Transformer: True
  Transformer配置: 2层, 4头

COOPERATIVE 模式详细结果:
--------------------------------------------------
模型架构:
  Actor参数数量: 3,730,953
  Critic参数数量: 494,285
  总参数数量: 4,225,238
训练结果:
  最佳验证奖励: 37.008589
测试性能:
  平均收益率: 0.565629
  平均距离: 10.849116
  平均内存使用: 0.001451
  平均功耗: 0.327555
  综合性能评分: 0.067230
文件路径:
  模型保存: constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\constellation_gpnindrnn_cooperative_transformer_L2H4_2025_08_12_14_35_14
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_cooperative/

COMPETITIVE 模式详细结果:
--------------------------------------------------
模型架构:
  Actor参数数量: 3,730,953
  Critic参数数量: 494,285
  总参数数量: 4,225,238
训练结果:
  最佳验证奖励: 33.469524
测试性能:
  平均收益率: 0.558828
  平均距离: 10.467727
  平均内存使用: -0.005578
  平均功耗: 0.321525
  综合性能评分: 0.190868
文件路径:
  模型保存: constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\constellation_gpnindrnn_competitive_transformer_L2H4_2025_08_12_14_54_18
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_competitive/

HYBRID 模式详细结果:
--------------------------------------------------
模型架构:
  Actor参数数量: 3,927,817
  Critic参数数量: 691,149
  总参数数量: 4,618,966
训练结果:
  最佳验证奖励: 35.770909
测试性能:
  平均收益率: 0.590913
  平均距离: 11.355146
  平均内存使用: 0.020350
  平均功耗: 0.339014
  综合性能评分: 0.051877
文件路径:
  模型保存: constellation_smp\constellation_smp150\multi_constellation_comparison_transformer_L2H4_2025_08_12_14_35_14\constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_12_15_20_46
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_hybrid/

对比分析:
==================================================
最佳奖励模式: cooperative (37.0086)
最佳收益率模式: hybrid (0.5909)
最短距离模式: competitive (10.4677)
最低功耗模式: competitive (0.3215)

推荐使用: cooperative 模式
推荐理由: 在关键性能指标上表现最佳
